#if CONFIG_AUTO_ENGINE_ENABLE_MNN
#include "AutoEngineMnn.hpp"
#include "ad_descrypt/ad_descrypt.h"
#include "utils.h"
#include <cmath>
#include <cstring>
#include <fstream>
#include <iostream>
#include <string>
#include <vector>

namespace AutoSDK {

AutoEngineMnn::AutoEngineMnn()
    : session_(nullptr), enable_zero_copy_(true), initialized_(false) {
  // 默认启用零拷贝优化
  LOG_INFO("MNN engine initialized with optimizations:");
  LOG_INFO("Zero-copy optimization: enabled");
  LOG_INFO("Memory pool: enabled");
  LOG_INFO("Tensor caching: enabled");
}

AutoEngineMnn::~AutoEngineMnn() { Release(); }

int AutoEngineMnn::Init(const std::vector<char> &model_data) {
  try {
    LOG_INFO("Initializing AutoEngineMnn");

    if (model_data.empty()) {
      LOG_ERROR("Model data is empty");
      return ENGINE_MODEL_LOAD_FAILED;
    }

    // 释放之前的资源
    Release();

    // 从内存加载模型
    int ret = LoadModelFromMemory(model_data);
    if (ret != ENGINE_SUCCESS) {
      LOG_ERROR("Failed to load MNN model from memory");
      return ret;
    }

    // 初始化输入输出信息
    ret = InitializeInputOutputInfo();
    if (ret != ENGINE_SUCCESS) {
      LOG_ERROR("Failed to initialize input/output info");
      return ret;
    }

    initialized_ = true;
    LOG_INFO("AutoEngineMnn initialized successfully");

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("AutoEngineMnn::Init failed with exception: ", e.what());
    return ENGINE_ERROR;
  }
}

int AutoEngineMnn::Release() {
  try {
    LOG_INFO("Starting MNN engine release...");

    // 1. 首先清理张量映射，避免访问已释放的张量
    input_tensors_.clear();
    output_tensors_.clear();

    // 2. 释放会话（这会释放MNN内部的张量内存）
    if (session_ && interpreter_) {
      LOG_INFO("Releasing MNN session...");
      interpreter_->releaseSession(session_);
      session_ = nullptr;
    }

    // 3. 清理主机张量缓存（必须在会话释放后）
    input_host_tensors_.clear();
    output_host_tensors_.clear();

    // 4. 释放解释器
    if (interpreter_) {
      LOG_INFO("Releasing MNN interpreter...");
      interpreter_.reset();
    }

    // 5. 最后清理输出数据指针（只释放我们自己分配的内存）
    for (auto &pair : output_data_ptrs_) {
      if (pair.second) {
        // 只释放非空指针（空指针表示使用了MNN内部内存）
        LOG_INFO("Freeing output buffer for: ", pair.first);
        free(pair.second);
        pair.second = nullptr;
      }
    }
    output_data_ptrs_.clear();
    output_data_sizes_.clear();

    // 清理其他资源
    input_names_.clear();
    output_names_.clear();
    input_infos_.clear();
    output_infos_.clear();

    initialized_ = false;
    LOG_INFO("AutoEngineMnn released successfully");

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("AutoEngineMnn::Release failed with exception: ", e.what());
    return ENGINE_ERROR;
  }
}

int AutoEngineMnn::LoadModelFromMemory(const std::vector<char> &model_data) {
  try {
    // 创建MNN解释器
    interpreter_ =
        std::shared_ptr<MNN::Interpreter>(MNN::Interpreter::createFromBuffer(
            model_data.data(), model_data.size()));

    if (!interpreter_) {
      LOG_ERROR("Failed to create MNN interpreter from memory");
      return ENGINE_MODEL_LOAD_FAILED;
    }

    // 配置会话参数
    if (model_info_.device_type == "GPU") {
      config_.type = MNN_FORWARD_OPENCL;
      LOG_INFO("Using GPU backend for MNN");
    } else if (model_info_.device_type == "CPU" ||
               model_info_.device_type == "DEFAULT") {
      config_.type = MNN_FORWARD_CPU;
      LOG_INFO("Using CPU backend for MNN");
    }
    config_.numThread = 4;
#if defined(__ANDROID__) || defined(ANDROID)
    config_.numThread = 1;
#endif

    // 配置后端
    backend_config_.precision =
        MNN::BackendConfig::Precision_Normal; // 默认使用FP32
    backend_config_.power = MNN::BackendConfig::Power_High;
    backend_config_.memory = MNN::BackendConfig::Memory_High;

    // 如果是OpenCL后端，添加特殊配置
    if (config_.type == MNN_FORWARD_OPENCL) {
      LOG_INFO("Using OpenCL backend for MNN");
      LOG_INFO("Forcing FP32 precision for OpenCL backend");

      // 禁用零拷贝优化，因为OpenCL需要特殊处理
      enable_zero_copy_ = true;
      LOG_INFO("Disabled zero-copy optimization for OpenCL backend");

      // 设置更多OpenCL特定配置
      // 例如：设置工作组大小、优先级等
    }

    config_.backendConfig = &backend_config_;

    // 创建会话
    session_ = interpreter_->createSession(config_);
    if (!session_) {
      LOG_ERROR("Failed to create MNN session");
      return ENGINE_MODEL_LOAD_FAILED;
    }

    LOG_INFO("Successfully loaded MNN model from memory");
    return ENGINE_SUCCESS;

  } catch (const std::exception &e) {
    LOG_ERROR("LoadModelFromMemory failed with exception: ", e.what());
    return ENGINE_MODEL_LOAD_FAILED;
  }
}

int AutoEngineMnn::InitializeInputOutputInfo() {
  try {
    if (!interpreter_ || !session_) {
      LOG_ERROR("Interpreter or session is null");
      return ENGINE_ERROR;
    }

    // 获取输入张量信息
    auto inputs = interpreter_->getSessionInputAll(session_);
    for (const auto &input : inputs) {
      input_names_.push_back(input.first);
      input_tensors_[input.first] = input.second;

      // 创建输入信息
      DataInfo input_info;
      input_info.name = input.first.c_str();

      auto shape = input.second->shape();
      input_info.dims.assign(shape.begin(), shape.end());

      input_info.data_layout = GetDataLayout(input.second);
      input_info.data_type = GetDataType(input.second);

      // 计算数据大小
      size_t total_size = 1;
      for (int dim : input_info.dims) {
        total_size *= dim;
      }
      input_info.size = total_size; // 假设为float类型

      input_infos_.push_back(input_info);
    }

    // 获取输出张量信息
    auto outputs = interpreter_->getSessionOutputAll(session_);
    for (const auto &output : outputs) {
      output_names_.push_back(output.first);
      output_tensors_[output.first] = output.second;

      // 创建输出信息
      DataInfo output_info;
      output_info.name = output.first.c_str();

      auto shape = output.second->shape();
      output_info.dims.assign(shape.begin(), shape.end());

      output_info.data_layout = GetDataLayout(output.second);
      output_info.data_type = GetDataType(output.second);

      // 计算数据大小
      size_t total_size = 1;
      for (int dim : output_info.dims) {
        total_size *= dim;
      }
      output_info.size = total_size; // 假设为float类型

      output_infos_.push_back(output_info);
    }

    LOG_INFO("Initialized input/output info - inputs: ", input_names_.size(),
             ", outputs: ", output_names_.size());

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("InitializeInputOutputInfo failed with exception: ", e.what());
    return ENGINE_ERROR;
  }
}

int AutoEngineMnn::GetInputsInfo(std::vector<DataInfo> &input_infos) {
  if (!initialized_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  input_infos.clear();
  input_infos.assign(input_infos_.begin(), input_infos_.end());
  //打印input_infos_
  for (const auto &input_info : input_infos_) {
    LOG_INFO("Engine Input info: ", input_info.name);
    for (const auto &dim : input_info.dims) {
      LOG_INFO("dim: ", dim);
    }
  }
  return ENGINE_SUCCESS;
}

int AutoEngineMnn::GetOutputsInfo(std::vector<DataInfo> &output_infos) {
  if (!initialized_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  output_infos.clear();
  output_infos.assign(output_infos_.begin(), output_infos_.end());

  //打印output_infos_
  for (const auto &output_info : output_infos_) {
    LOG_INFO("Engine Output info: ", output_info.name);
    for (const auto &dim : output_info.dims) {
      LOG_INFO("dim: ", dim);
    }
  }
  return ENGINE_SUCCESS;
}

int AutoEngineMnn::SetBlobData(std::vector<EngineData> &input_datas) {
  auto start = EngineUtils::getTimeOfMSeconds();
  if (!initialized_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  try {
    for (const auto &input_data : input_datas) {
      std::string tensor_name(input_data.data_info.name);

      auto it = input_tensors_.find(tensor_name);
      if (it == input_tensors_.end()) {
        LOG_ERROR("Input tensor not found: ", tensor_name);
        return ENGINE_INPUT_ERROR;
      }

      MNN::Tensor *input_tensor = it->second;
      int ret = ConvertToMnnTensor(input_data, input_tensor);
      if (ret != ENGINE_SUCCESS) {
        LOG_ERROR("Failed to convert data to MNN tensor for: ", tensor_name);
        return ret;
      }
    }
    auto end = EngineUtils::getTimeOfMSeconds();
    LOG_INFO("SetBlobData time: ", end - start, " ms");

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("SetBlobData failed with exception: ", e.what());
    return ENGINE_INPUT_ERROR;
  }
}

int AutoEngineMnn::GetBlobData(std::vector<EngineData> &output_datas) {
  auto start = EngineUtils::getTimeOfMSeconds();
  if (!initialized_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  try {
    output_datas.clear();

    for (const auto &output_name : output_names_) {
      auto it = output_tensors_.find(output_name);
      if (it == output_tensors_.end()) {
        LOG_ERROR("Output tensor not found: ", output_name);
        return ENGINE_OUTPUT_ERROR;
      }

      EngineData output_data;
      int ret = ConvertFromMnnTensor(it->second, output_data, output_name);
      if (ret != ENGINE_SUCCESS) {
        LOG_ERROR("Failed to convert MNN tensor to data for: ", output_name);
        return ret;
      }

      output_datas.push_back(output_data);
    }
    auto end = EngineUtils::getTimeOfMSeconds();
    LOG_INFO("GetBlobData time: ", end - start, " ms");

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("GetBlobData failed with exception: ", e.what());
    return ENGINE_OUTPUT_ERROR;
  }
}

int AutoEngineMnn::Forward() {
  if (!initialized_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  try {
    double start = EngineUtils::getTimeOfMSeconds();
    // 执行推理
    int ret = interpreter_->runSession(session_);
    double end = EngineUtils::getTimeOfMSeconds();
    LOG_INFO("MNN inference time: ", end - start, " ms");
    if (ret != 0) {
      LOG_ERROR("MNN inference failed with error code: ", ret);
      return ENGINE_INFERENCE_FAILED;
    }

    LOG_INFO("MNN inference completed successfully");
    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("Forward failed with exception: ", e.what());
    return ENGINE_INFERENCE_FAILED;
  }
}

int AutoEngineMnn::ConvertToMnnTensor(const EngineData &engine_data,
                                      MNN::Tensor *mnn_tensor) {
  try {
    if (!engine_data.data || !mnn_tensor) {
      LOG_ERROR("Invalid input data or tensor");
      return ENGINE_INPUT_ERROR;
    }

    std::string tensor_name(engine_data.data_info.name);
    size_t data_size = mnn_tensor->elementSize() * sizeof(float);

    // 尝试零拷贝优化
    if (enable_zero_copy_ && engine_data.type == EngineMemoryType::MEM_ON_CPU) {
      // 检查数据是否已经是正确的格式和对齐
      if (reinterpret_cast<uintptr_t>(engine_data.data) % sizeof(float) == 0) {
        // 为OpenCL后端创建主机张量，确保内存分配
        auto host_tensor =
            std::make_shared<MNN::Tensor>(mnn_tensor, MNN::Tensor::CAFFE, true);

        // 安全的零拷贝：检查是否可以直接使用用户内存
        float *user_data = static_cast<float *>(engine_data.data);
        float *tensor_data = host_tensor->host<float>();

        if (tensor_data && user_data) {
          // 直接拷贝，但减少一次中间缓冲
          memcpy(tensor_data, user_data, data_size);
          LOG_INFO("Applied optimized copy for input tensor: ", tensor_name);
        } else {
          LOG_ERROR("Failed to apply zero-copy for input tensor: ",
                    tensor_name);
          return ENGINE_INPUT_ERROR;
        }

        // 缓存主机张量以重用
        input_host_tensors_[tensor_name] = host_tensor;

        // 将主机张量数据拷贝到设备张量
        mnn_tensor->copyFromHostTensor(host_tensor.get());

        return ENGINE_SUCCESS;
      }
    }

    // 回退到传统方式
    auto it = input_host_tensors_.find(tensor_name);
    std::shared_ptr<MNN::Tensor> host_tensor;

    if (config_.type == MNN_FORWARD_OPENCL) {
      // OpenCL后端：每次创建新的主机张量
      host_tensor =
          std::make_shared<MNN::Tensor>(mnn_tensor, MNN::Tensor::CAFFE, true);
      LOG_INFO("Created new host tensor for OpenCL input: ", tensor_name);
    } else if (it != input_host_tensors_.end()) {
      // 重用缓存的主机张量
      host_tensor = it->second;
    } else {
      // 创建新的主机张量
      host_tensor =
          std::make_shared<MNN::Tensor>(mnn_tensor, MNN::Tensor::CAFFE, true);
      input_host_tensors_[tensor_name] = host_tensor;
    }

    // 拷贝数据到主机张量
    memcpy(host_tensor->host<float>(), engine_data.data, data_size);

    // 将主机张量数据拷贝到设备张量
    mnn_tensor->copyFromHostTensor(host_tensor.get());

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("ConvertToMnnTensor failed with exception: ", e.what());
    return ENGINE_INPUT_ERROR;
  }
}

int AutoEngineMnn::ConvertFromMnnTensor(MNN::Tensor *mnn_tensor,
                                        EngineData &engine_data,
                                        const std::string &name) {
  try {
    if (!mnn_tensor) {
      LOG_ERROR("Invalid MNN tensor");
      return ENGINE_OUTPUT_ERROR;
    }

    // 对于OpenCL后端，每次都创建新的主机张量以确保内存正确分配
    std::shared_ptr<MNN::Tensor> host_tensor;

    if (config_.type == MNN_FORWARD_OPENCL) {
      // OpenCL后端：创建新的主机张量，确保内存分配
      host_tensor =
          std::make_shared<MNN::Tensor>(mnn_tensor, MNN::Tensor::CAFFE, true);
      LOG_INFO("Created new host tensor for OpenCL backend: ", name);
    } else {
      // 其他后端：可以重用缓存的主机张量
      auto host_it = output_host_tensors_.find(name);
      if (host_it != output_host_tensors_.end()) {
        host_tensor = host_it->second;
      } else {
        host_tensor =
            std::make_shared<MNN::Tensor>(mnn_tensor, MNN::Tensor::CAFFE, true);
        output_host_tensors_[name] = host_tensor;
      }
    }

    size_t data_size = mnn_tensor->elementSize() * sizeof(float);

    // 确保主机张量有足够的内存
    if (!host_tensor->host<float>()) {
      LOG_ERROR("Host tensor has no memory allocated for: ", name);
      return ENGINE_OUTPUT_ERROR;
    }

    // 将设备张量数据拷贝到主机张量（对于OpenCL后端这是关键步骤）
    bool copy_success = mnn_tensor->copyToHostTensor(host_tensor.get());
    if (!copy_success) {
      LOG_ERROR("Failed to copy from device tensor to host tensor for: ", name);
      return ENGINE_OUTPUT_ERROR;
    }

    LOG_INFO("Successfully copied device tensor to host for: ", name);

    // 内存池优化：检查是否需要重新分配内存
    auto size_it = output_data_sizes_.find(name);
    auto ptr_it = output_data_ptrs_.find(name);

    bool need_realloc = false;
    if (ptr_it == output_data_ptrs_.end() || !ptr_it->second) {
      need_realloc = true;
    } else if (size_it == output_data_sizes_.end() ||
               size_it->second < data_size) {
      // 需要更大的内存，释放旧的并重新分配
      free(ptr_it->second);
      need_realloc = true;
    }

    if (need_realloc) {
      output_data_ptrs_[name] = (float *)malloc(data_size);
      if (!output_data_ptrs_[name]) {
        LOG_ERROR("Failed to allocate memory for output: ", name);
        return ENGINE_OUTPUT_ERROR;
      }
      output_data_sizes_[name] = data_size;
    }

    // 安全的输出拷贝，特别针对OpenCL后端优化
    float *host_data = host_tensor->host<float>();
    if (!output_data_ptrs_[name]) {
      LOG_ERROR("Output buffer not allocated for: ", name);
      return ENGINE_OUTPUT_ERROR;
    }

    if (!host_data) {
      LOG_ERROR("Host tensor data is null for: ", name);
      return ENGINE_OUTPUT_ERROR;
    }

    // 验证数据指针的有效性
    if (reinterpret_cast<uintptr_t>(host_data) % sizeof(float) != 0) {
      LOG_ERROR("Host tensor data is not properly aligned for: ", name);
      return ENGINE_OUTPUT_ERROR;
    }

    // 执行安全的内存拷贝
    memcpy(output_data_ptrs_[name], host_data, data_size);
    engine_data.data = output_data_ptrs_[name];

    LOG_INFO("Successfully copied output data for tensor: ", name, " (",
             data_size, " bytes)");
    engine_data.type = EngineMemoryType::MEM_ON_CPU;
    engine_data.device_id = 1;

    // 设置数据信息
    engine_data.data_info.name = name;
    auto shape = mnn_tensor->shape();
    engine_data.data_info.dims.assign(shape.begin(), shape.end());
    engine_data.data_info.size = data_size;
    engine_data.data_info.data_layout = GetDataLayout(mnn_tensor);
    engine_data.data_info.data_type = GetDataType(mnn_tensor);

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("ConvertFromMnnTensor failed with exception: ", e.what());
    return ENGINE_OUTPUT_ERROR;
  }
}

ModelDataLayOut AutoEngineMnn::GetDataLayout(MNN::Tensor *tensor) {
  if (!tensor) {
    return ModelDataLayOut::MODEL_DATA_LAYOUT_UNKNOWN;
  }

  // MNN默认使用NCHW格式
  return ModelDataLayOut::MODEL_DATA_LAYOUT_NCHW;
}

DataType AutoEngineMnn::GetDataType(MNN::Tensor *tensor) {
  if (!tensor) {
    return DataType::DATA_TYPE_FLOAT32;
  }

  // 根据MNN张量的类型返回对应的数据类型
  auto type = tensor->getType();
  switch (type.code) {
  case halide_type_float:
    if (type.bits == 32) {
      return DataType::DATA_TYPE_FLOAT32;
    } else if (type.bits == 16) {
      return DataType::DATA_TYPE_FLOAT16;
    }
    break;
  case halide_type_int:
    if (type.bits == 32) {
      return DataType::DATA_TYPE_INT32;
    } else if (type.bits == 8) {
      return DataType::DATA_TYPE_INT8;
    }
    break;
  case halide_type_uint:
    if (type.bits == 8) {
      return DataType::DATA_TYPE_UINT8;
    }
    break;
  default:
    break;
  }

  return DataType::DATA_TYPE_FLOAT32; // 默认返回float32
}

void AutoEngineMnn::UpdateOutputInfo() {
  // 更新输出信息（如果需要的话）
  // 在MNN中，输出信息在初始化时就已经确定，通常不需要动态更新
}

} // namespace AutoSDK

#endif // CONFIG_AUTO_ENGINE_ENABLE_MNN
