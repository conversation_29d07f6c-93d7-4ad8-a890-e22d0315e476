#!/bin/bash
source /mnt/nfs/profile
# TARGET_OS=linux TARGET_ARCH=x86_64 cppbuild dep
# TARGET_OS=linux TARGET_ARCH=x86_64 cppbuild dep --cfg="enable_onnx"
TARGET_OS=linux TARGET_ARCH=x86_64 cppbuild dep --cfg="enable_ncnn,enable_mnn"
TARGET_OS=linux TARGET_ARCH=x86_64 cppbuild build --build-type=release --install
# TARGET_OS=linux TARGET_ARCH=x86_64 TARGET_CPU_TYPE=gcc7 cppbuild dep --cfg="enable_ncnn,enable_mnn"
# TARGET_OS=linux TARGET_ARCH=x86_64 TARGET_CPU_TYPE=gcc7 cppbuild build --build-type=release --install
#aarch64
# TARGET_OS=linux TARGET_ARCH=aarch64 TARGET_CPU_TYPE=gcc10_2 cppbuild dep --cfg="enable_tensorrt"
# TARGET_OS=linux TARGET_ARCH=aarch64 TARGET_CPU_TYPE=gcc8_2 cppbuild build --build-type=release --install
# TARGET_OS=linux TARGET_ARCH=aarch64 TARGET_CPU_TYPE=gcc10_2  cppbuild build --build-type=release --install

# TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild dep
# TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild dep --cfg="enable_ppl3_dsp"
# TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild dep --cfg="enable_ppl3"
# TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild dep --cfg="enable_qnn"
# TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild dep --cfg="enable_snpe"
# TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild dep --cfg="enable_rknn"
source /mnt/nfs/profile
TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild dep --cfg="enable_ncnn,enable_mnn"
TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild build --build-type=release --install

